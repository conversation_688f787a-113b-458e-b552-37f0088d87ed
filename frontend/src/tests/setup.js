import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock scrollTo
window.scrollTo = vi.fn();

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to ignore specific console methods
  // log: vi.fn(),
  // debug: vi.fn(),
  // info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Setup fetch mock
global.fetch = vi.fn();

// Mock react-hot-toast
const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  loading: vi.fn(),
  dismiss: vi.fn(),
};

vi.mock('react-hot-toast', () => ({
  default: mockToast,
  toast: mockToast,
  Toaster: () => null,
}));

// Export mock for test access
global.mockToast = mockToast;

// Mock react-router-dom with comprehensive Router system
vi.mock('react-router-dom', async () => {
  const React = require('react');
  const actual = await vi.importActual('react-router-dom');

  // Mock Routes component that renders the first matching Route
  const MockRoutes = ({ children }) => {
    const routes = React.Children.toArray(children);
    // For testing, render the first route (usually the default route)
    const firstRoute = routes.find(
      route => route.props?.path === '/' || route.props?.index
    );
    if (firstRoute && firstRoute.props?.element) {
      return React.createElement(
        'div',
        { 'data-testid': 'routes' },
        firstRoute.props.element
      );
    }
    // Fallback: render all route elements
    return React.createElement(
      'div',
      { 'data-testid': 'routes' },
      routes
        .map((route, index) =>
          route.props?.element
            ? React.createElement(
                'div',
                { key: index, 'data-testid': `route-${index}` },
                route.props.element
              )
            : null
        )
        .filter(Boolean)
    );
  };

  // Mock Route component
  const MockRoute = ({ element, path, ...props }) => {
    return React.createElement(
      'div',
      { 'data-testid': 'route', 'data-path': path },
      element
    );
  };

  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/',
      search: '',
      hash: '',
      state: null,
      key: 'default',
    }),
    useParams: () => ({}),
    useSearchParams: () => [new URLSearchParams(), vi.fn()],
    BrowserRouter: ({ children }) =>
      React.createElement('div', { 'data-testid': 'browser-router' }, children),
    Routes: MockRoutes,
    Route: MockRoute,
    Link: ({ children, to, ...props }) => {
      return React.createElement(
        'a',
        { href: to, ...props, 'data-testid': 'router-link' },
        children
      );
    },
    NavLink: ({ children, to, ...props }) => {
      return React.createElement(
        'a',
        { href: to, ...props, 'data-testid': 'nav-link', className: 'active' },
        children
      );
    },
    Outlet: () => React.createElement('div', { 'data-testid': 'outlet' }),
  };
});

// Mock D3 for chart components
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      remove: vi.fn(),
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({
        attr: vi.fn(),
      })),
    })),
  })),
  hierarchy: vi.fn(),
  treemap: vi.fn(() => ({
    size: vi.fn(() => ({
      paddingOuter: vi.fn(() => ({
        paddingInner: vi.fn(() => ({
          round: vi.fn(),
        })),
      })),
    })),
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(),
    })),
  })),
}));

// Mock environment variables
vi.mock('../constants/config.js', () => ({
  API_BASE_URL: 'http://localhost:5001',
  API_ENDPOINTS: {
    OVERVIEW: '/api/analytics/overview',
    ACCOUNTS: '/api/accounts',
    TRANSACTIONS: '/api/transactions',
    MARKET_HOTSPOTS: '/api/market/hotspots',
    STRATEGY_ANALYSIS: '/api/strategies/analysis',
    REALIZED_GAINS: '/api/analytics/realized_gains',
  },
}));

// Mock axios
vi.mock('axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
}));

// Mock MUI components that might cause issues in tests
vi.mock('@mui/x-date-pickers', () => ({
  DatePicker: ({ children, ...props }) => {
    const React = require('react');
    return React.createElement(
      'div',
      { 'data-testid': 'date-picker', ...props },
      children
    );
  },
  LocalizationProvider: ({ children }) => children,
}));

// Mock echarts-for-react
vi.mock('echarts-for-react', () => ({
  default: ({ option, ...props }) => {
    const React = require('react');
    return React.createElement(
      'div',
      { 'data-testid': 'echarts', ...props },
      JSON.stringify(option)
    );
  },
}));

// Mock recharts
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }) => {
    const React = require('react');
    return React.createElement(
      'div',
      { 'data-testid': 'responsive-container' },
      children
    );
  },
  LineChart: ({ children, ...props }) => {
    const React = require('react');
    return React.createElement(
      'div',
      { 'data-testid': 'line-chart', ...props },
      children
    );
  },
  Line: props => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'line', ...props });
  },
  XAxis: props => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'x-axis', ...props });
  },
  YAxis: props => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'y-axis', ...props });
  },
  CartesianGrid: props => {
    const React = require('react');
    return React.createElement('div', {
      'data-testid': 'cartesian-grid',
      ...props,
    });
  },
  Tooltip: props => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'tooltip', ...props });
  },
  Legend: props => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'legend', ...props });
  },
}));

// Global test cleanup
afterEach(() => {
  vi.clearAllMocks();
  // Reset fetch mock
  global.fetch.mockClear();
});
