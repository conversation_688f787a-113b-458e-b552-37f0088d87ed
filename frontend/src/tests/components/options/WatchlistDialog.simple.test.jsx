import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import WatchlistDialog from '../../../components/options/WatchlistDialog'
import { createMockFetch } from '../../mocks/apiMocks'

describe('WatchlistDialog', () => {
  const mockProps = {
    open: true,
    onClose: vi.fn(),
    selectedAccount: 1,
    onWatchlistsUpdated: vi.fn()
  }
  
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = createMockFetch()
  })

  it('renders when open', () => {
    render(<WatchlistDialog {...mockProps} />)
    
    expect(screen.getByText('管理观察列表')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<WatchlistDialog {...mockProps} open={false} />)
    
    expect(screen.queryByText('管理观察列表')).not.toBeInTheDocument()
  })

  it('shows loading state initially', () => {
    render(<WatchlistDialog {...mockProps} />)
    
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })
})
