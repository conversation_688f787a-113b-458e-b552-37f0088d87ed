import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import StrategyConfigDialog from '../../../components/options/StrategyConfigDialog'
import { createMockFetch } from '../../mocks/apiMocks'

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  }
}))

// Mock the config to include options endpoints
vi.mock('../../../constants/config', () => ({
  API_BASE_URL: 'http://localhost:5001',
  API_ENDPOINTS: {
    OPTIONS_CONFIG: '/api/strategies/options/config',
  }
}))

describe('StrategyConfigDialog', () => {
  const mockProps = {
    open: true,
    onClose: vi.fn(),
    selectedAccount: 1,
    strategyType: 'cash_secured_puts',
    currentConfig: {
      min_dte: 20,
      max_dte: 60,
      min_annual_roi: 0.15,
      max_delta: 0.30
    },
    onConfigSaved: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = createMockFetch()
  })

  it('renders when open', () => {
    render(<StrategyConfigDialog {...mockProps} />)

    expect(screen.getByText('策略配置')).toBeInTheDocument()
    expect(screen.getByText('基础设置')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<StrategyConfigDialog {...mockProps} open={false} />)

    expect(screen.queryByText('策略配置')).not.toBeInTheDocument()
  })

  it('displays correct strategy type title', () => {
    const { rerender } = render(<StrategyConfigDialog {...mockProps} strategyType="cash_covered_options" />)

    expect(screen.getByText('现金担保期权配置')).toBeInTheDocument()

    rerender(<StrategyConfigDialog {...mockProps} strategyType="iron_condors" />)
    expect(screen.getByText('铁鹰策略配置')).toBeInTheDocument()
  })

  it('loads current configuration values', () => {
    render(<StrategyConfigDialog {...mockProps} />)

    expect(screen.getByDisplayValue('20')).toBeInTheDocument() // min_dte
    expect(screen.getByDisplayValue('60')).toBeInTheDocument() // max_dte
    expect(screen.getByDisplayValue('0.15')).toBeInTheDocument() // min_annual_roi (as decimal with % symbol)
  })

  it('shows appropriate fields for cash secured puts strategy', () => {
    render(<StrategyConfigDialog {...mockProps} strategyType="cash_secured_puts" />)

    expect(screen.getByText('最小到期天数')).toBeInTheDocument()
    expect(screen.getByText('最大到期天数')).toBeInTheDocument()
    expect(screen.getByText('最小年化收益率')).toBeInTheDocument()
  })

  it('shows appropriate fields for covered calls strategy', () => {
    render(<StrategyConfigDialog {...mockProps} strategyType="covered_calls" />)

    expect(screen.getByText('最小到期天数')).toBeInTheDocument()
    expect(screen.getByText('最大到期天数')).toBeInTheDocument()
    expect(screen.getByText('最小年化收益率')).toBeInTheDocument()
  })

  it('shows appropriate fields for iron condors strategy', () => {
    render(<StrategyConfigDialog {...mockProps} strategyType="iron_condors" />)

    expect(screen.getByText('最小到期天数')).toBeInTheDocument()
    expect(screen.getByText('最大到期天数')).toBeInTheDocument()
    expect(screen.getByText('最小年化收益率')).toBeInTheDocument()
  })

  it('validates input values', async () => {
    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const saveButton = screen.getByText('保存配置')
    await user.click(saveButton)

    // Should attempt to save (validation happens on backend)
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled()
    })
  })

  it('validates percentage fields', async () => {
    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const saveButton = screen.getByText('保存配置')
    await user.click(saveButton)

    // Should attempt to save (validation happens on backend)
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled()
    })
  })

  it('saves configuration successfully', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ config_id: 1, status: 'success' })
    })

    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    // Save configuration
    const saveButton = screen.getByText('保存配置')
    await user.click(saveButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:5001/api/strategies/options/config/cash_secured_puts',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: expect.stringContaining('min_dte')
        })
      )
    })

    expect(mockProps.onConfigSaved).toHaveBeenCalledWith(
      expect.objectContaining({
        min_dte: 20,
        max_dte: 60,
        min_annual_roi: 0.15,
        max_delta: 0.3
      })
    )
    expect(mockProps.onClose).toHaveBeenCalled()
  })

  it('handles save errors gracefully', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: false,
      json: () => Promise.resolve({ error: 'Save failed' })
    })

    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const saveButton = screen.getByText('保存配置')
    await user.click(saveButton)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled()
    })

    // Should not close dialog on error
    expect(mockProps.onClose).not.toHaveBeenCalled()
    expect(mockProps.onConfigSaved).not.toHaveBeenCalled()
  })

  it('resets to defaults when reset button is clicked', async () => {
    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    // Click reset button
    const resetButton = screen.getByText('重置')
    await user.click(resetButton)

    // Should reset to default values (check that original values are still there)
    expect(screen.getByDisplayValue('20')).toBeInTheDocument()
  })

  it('closes dialog when cancel button is clicked', async () => {
    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const cancelButton = screen.getByText('取消')
    await user.click(cancelButton)

    expect(mockProps.onClose).toHaveBeenCalled()
  })

  it('shows loading state during save', async () => {
    // Mock a slow response
    global.fetch = vi.fn().mockImplementation(() => new Promise(resolve => {
      setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve({ config_id: 1, status: 'success' })
      }), 100)
    }))

    const user = userEvent.setup()
    render(<StrategyConfigDialog {...mockProps} />)

    const saveButton = screen.getByText('保存配置')
    await user.click(saveButton)

    // Should show loading state
    expect(screen.getByText('保存中...')).toBeInTheDocument()
    expect(saveButton).toBeDisabled()

    // Wait for completion
    await waitFor(() => {
      expect(screen.queryByText('保存中...')).not.toBeInTheDocument()
    })
  })

  it('handles different strategy types correctly', () => {
    const { rerender } = render(<StrategyConfigDialog {...mockProps} strategyType="covered_calls" />)

    expect(screen.getByText('策略配置')).toBeInTheDocument()

    rerender(<StrategyConfigDialog {...mockProps} strategyType="iron_condors" />)
    expect(screen.getByText('铁鹰策略配置')).toBeInTheDocument()
  })

  it('preserves form state when strategy type changes', async () => {
    const user = userEvent.setup()
    const { rerender } = render(<StrategyConfigDialog {...mockProps} />)

    // Change strategy type
    rerender(<StrategyConfigDialog {...mockProps} strategyType="iron_condors" />)

    // Should still show the dialog
    expect(screen.getByText('铁鹰策略配置')).toBeInTheDocument()
  })
})
