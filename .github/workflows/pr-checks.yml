name: Pull Request Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened, ready_for_review]

# Cancel previous runs for the same PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  # Quick validation checks
  validate:
    name: Quick Validation
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate workflow files
        run: |
          # Check if workflow files are valid YAML
          for file in .github/workflows/*.yml; do
            echo "Validating $file"
            python -c "import yaml; yaml.safe_load(open('$file'))"
          done

      - name: Check for secrets in code
        run: |
          # Simple check for potential secrets
          if grep -r -i "password\|secret\|key\|token" --include="*.py" --include="*.js" --include="*.ts" --exclude-dir=node_modules .; then
            echo "⚠️ Potential secrets found in code. Please review."
            exit 1
          fi

      - name: Check file sizes
        run: |
          # Check for large files that shouldn't be committed
          find . -type f -size +10M -not -path "./.git/*" -not -path "./node_modules/*" -not -path "./.venv/*" | while read file; do
            echo "⚠️ Large file detected: $file"
            exit 1
          done

  # Backend tests
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    strategy:
      matrix:
        python-version: ['3.11', '3.12']
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'

      - name: Install dependencies
        working-directory: ./backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest-cov pytest-xdist

      - name: Initialize database
        working-directory: ./backend
        run: |
          mkdir -p data logs
          # Initialize database with schema
          sqlite3 data/stock_trading.db < config/schema.sql

      - name: Run tests
        working-directory: ./backend
        run: |
          python -m pytest tests/ \
            --cov=src \
            --cov-report=xml \
            --cov-fail-under=75 \
            -n auto \
            --tb=short

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./backend/coverage.xml
          flags: backend-pr
          name: backend-${{ matrix.python-version }}

  # Frontend tests
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    strategy:
      matrix:
        node-version: ['18', '20']
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Run tests
        working-directory: ./frontend
        run: npm run test:coverage

      - name: Build
        working-directory: ./frontend
        run: npm run build

  # Code quality checks
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Python tools
        run: |
          pip install black isort flake8 mypy

      - name: Check Python formatting
        working-directory: ./backend
        run: |
          black --check --diff .
          isort --check-only --diff .

      - name: Run Python linting
        working-directory: ./backend
        run: |
          flake8 . --count --statistics

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Check frontend formatting
        working-directory: ./frontend
        run: |
          npm run lint
          npm run format:check

  # Security checks
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'

      - name: Python security check
        working-directory: ./backend
        run: |
          pip install safety
          safety check

      - name: Node.js security audit
        working-directory: ./frontend
        run: |
          npm audit --audit-level=moderate

  # Performance tests
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false && contains(github.event.pull_request.labels.*.name, 'performance')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install dependencies
        working-directory: ./backend
        run: |
          pip install -r requirements.txt
          pip install pytest-benchmark

      - name: Initialize database
        working-directory: ./backend
        run: |
          mkdir -p data logs
          # Initialize database with schema
          sqlite3 data/stock_trading.db < config/schema.sql

      - name: Run performance tests
        working-directory: ./backend
        run: |
          python -m pytest tests/ -m "performance" --benchmark-only

  # Comment on PR with results
  comment-results:
    name: Comment Results
    runs-on: ubuntu-latest
    needs: [validate, backend-tests, frontend-tests, code-quality, security]
    if: always() && github.event.pull_request.draft == false
    
    steps:
      - name: Comment PR
        uses: actions/github-script@v7
        with:
          script: |
            const results = {
              validate: '${{ needs.validate.result }}',
              backend: '${{ needs.backend-tests.result }}',
              frontend: '${{ needs.frontend-tests.result }}',
              quality: '${{ needs.code-quality.result }}',
              security: '${{ needs.security.result }}'
            };
            
            const passed = Object.values(results).filter(r => r === 'success').length;
            const total = Object.keys(results).length;
            
            const emoji = passed === total ? '✅' : '❌';
            const status = passed === total ? 'All checks passed!' : `${passed}/${total} checks passed`;
            
            const body = `## ${emoji} PR Checks Results
            
            ${status}
            
            | Check | Status |
            |-------|--------|
            | Validation | ${results.validate === 'success' ? '✅' : '❌'} |
            | Backend Tests | ${results.backend === 'success' ? '✅' : '❌'} |
            | Frontend Tests | ${results.frontend === 'success' ? '✅' : '❌'} |
            | Code Quality | ${results.quality === 'success' ? '✅' : '❌'} |
            | Security | ${results.security === 'success' ? '✅' : '❌'} |
            
            ${passed === total ? '🚀 Ready to merge!' : '⚠️ Please fix failing checks before merging.'}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            });
