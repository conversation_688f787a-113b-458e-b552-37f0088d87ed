name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.12'
  NODE_VERSION: '18'

jobs:
  # Backend Testing Job
  backend-tests:
    name: Backend Tests & Coverage
    runs-on: ubuntu-latest
    
    services:
      # SQLite is file-based, so we don't need a service container
      # But we can add Redis if needed for caching
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: Create virtual environment
        run: |
          python -m venv .venv
          source .venv/bin/activate
          echo "VIRTUAL_ENV=$VIRTUAL_ENV" >> $GITHUB_ENV
          echo "$VIRTUAL_ENV/bin" >> $GITHUB_PATH

      - name: Install dependencies
        working-directory: ./backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest-cov pytest-xdist pytest-mock

      - name: Create test directories and files
        working-directory: ./backend
        run: |
          mkdir -p data/cache logs
          touch data/stock_trading.db
          # Create test environment file
          echo "LOG_ENV=TESTING" > .env
          echo "DATABASE_URL=sqlite:///data/test_stock_trading.db" >> .env

      - name: Run linting
        working-directory: ./backend
        run: |
          pip install flake8 black isort
          # Check code formatting
          black --check --diff .
          # Check import sorting
          isort --check-only --diff .
          # Run flake8 linting
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

      - name: Run unit tests with coverage
        working-directory: ./backend
        run: |
          python -m pytest tests/ \
            --cov=src \
            --cov-report=xml \
            --cov-report=html \
            --cov-report=term-missing \
            --cov-fail-under=75 \
            --junitxml=test-results.xml \
            -v \
            --tb=short \
            --maxfail=10

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./backend/coverage.xml
          directory: ./backend
          flags: backend
          name: backend-coverage
          fail_ci_if_error: false

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: backend-test-results
          path: |
            backend/test-results.xml
            backend/htmlcov/
          retention-days: 30

  # Frontend Testing Job
  frontend-tests:
    name: Frontend Tests & Build
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: frontend/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Run linting
        working-directory: ./frontend
        run: |
          npm run lint
          npm run format:check

      - name: Run unit tests
        working-directory: ./frontend
        run: npm run test:coverage

      - name: Build application
        working-directory: ./frontend
        run: npm run build

      - name: Upload frontend coverage
        uses: codecov/codecov-action@v4
        with:
          directory: ./frontend/coverage
          flags: frontend
          name: frontend-coverage
          fail_ci_if_error: false

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: frontend/dist/
          retention-days: 30


  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Set up Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: frontend/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install backend dependencies
        working-directory: ./backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Build frontend
        working-directory: ./frontend
        run: npm run build

      - name: Start backend server
        working-directory: ./backend
        run: |
          # Create test database and setup
          mkdir -p data logs
          touch data/stock_trading.db
          echo "LOG_ENV=TESTING" > .env
          # Start server in background
          python app.py &
          echo $! > server.pid
          # Wait for server to start
          sleep 10

      - name: Run integration tests
        working-directory: ./backend
        run: |
          # Run API integration tests
          python -m pytest tests/integration/ -v --tb=short

      - name: Stop backend server
        if: always()
        working-directory: ./backend
        run: |
          if [ -f server.pid ]; then
            kill $(cat server.pid) || true
            rm server.pid
          fi

  # Docker Build and Test
  docker-build:
    name: Docker Build & Test
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker images
        run: |
          docker-compose build --no-cache

      - name: Test Docker containers
        run: |
          # Start containers
          docker-compose up -d
          
          # Wait for services to be ready
          sleep 30
          
          # Test backend health
          curl -f http://localhost:5000/health || exit 1
          
          # Test frontend
          curl -f http://localhost:3000 || exit 1

      - name: Stop containers
        if: always()
        run: docker-compose down

  # Deployment (only on main branch)
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, integration-tests, docker-build]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    environment:
      name: production
      url: https://your-app-domain.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production
        run: |
          echo "🚀 Deploying to production..."
          # Add your deployment commands here
          # Examples:
          # - Deploy to AWS ECS/EKS
          # - Deploy to Heroku
          # - Deploy to DigitalOcean
          # - Deploy to your own servers
          echo "✅ Deployment completed!"
