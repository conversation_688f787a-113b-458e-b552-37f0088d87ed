node_modules/
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
__pycache__/
.DS_Store
venv/
logs/

# Python
*.pyc
*.pyo
*.pyd
*.egg-info
*.sqlite
*.ipynb_checkpoints
.pytest_cache/

# Frontend
.cache/
.vite/
yarn.lock
pnpm-lock.yaml
npm-debug.log*

# IDEs
.vscode/
.idea/

# Testing
.coverage
htmlcov/

# Local development
.env.*.local
settings.py

# Database (from your setup instructions)
backend/data/stock_trading.db

# Build files
build/


# Ignore all files in the logs directory
logs/*

# Ignore all files in the cache directory
backend/data/cache/*
*.parquet

# Ignore model data
backend/data/models/*

.augment/

# Coverage files and reports
coverage.xml
.coverage
.coverage.*
coverage.json
htmlcov/
.nyc_output/
coverage/
.lcov
lcov.info
*.lcov
.pytest_cache/
.vitest/
coverage-final.json
coverage-summary.json

# Frontend coverage (Vitest/Jest)
coverage/
coverage-ts/
.nyc_output/
junit.xml

# Python coverage tools
.coverage
.coverage.*
htmlcov/
.tox/
.nox/
.pytest_cache/
coverage.xml
coverage.json
*.cover
*.py,cover
.hypothesis/

# Build outputs
frontend/dist/*